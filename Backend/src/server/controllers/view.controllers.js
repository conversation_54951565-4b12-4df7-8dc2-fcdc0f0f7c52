import View from "../models/view.model.js";
import Folder from "../models/folder.model.js";
import PannelType from "../models/pannelType.model.js";
import CSVFilesSchema from "../models/file.model.js";
import ViewPannel from "../models/viewPannel.model.js";
export const createView = async (req, res) => {
    const { name, folder_id, csvfile_id, structure, panels } = req.body;

    try {
        // Create the view
        const newView = await View.create({
            user_id: req.user.id,
            folder_id,
            name,
            structure: structure || {},
            csvfile_id
        });

        // Create panels if provided
        if (panels && Array.isArray(panels)) {
            for (const panel of panels) {
                await ViewPannel.create({
                    views_id: newView.id,
                    pannel_type_id: panel.pannel_type_id,
                    configuration: panel.configuration
                });
            }
        }

        return res.status(201).json({
            status: 201,
            message: "View created successfully.",
            data: newView
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({ status: 500, error: 'Failed to create view' });
    }
};

export const deleteView = async (req, res) => {
    const { id, type } = req.params;

    try {
        if(type == 'file'){
            const view = await View.findByPk(id);

            if (!view) {
                return res.status(404).json({ error: 'View not found' });
            }
            // const result = await MLJob.deleteMany({ workflow_id: id });
            // const mljobdata = await MLJobData.deleteMany({ workflow_id: id });
    
            await view.destroy();
    
            return res.status(200).json({ message: 'View deleted successfully' });
        }
        else if(type == 'folder') {
            const folder = await Folder.findByPk(id);

            if (!folder) {
                return res.status(404).json({ error: 'Folder not found' });
            }
    
            await folder.destroy();
    
            return res.status(200).json({ message: 'Folder deleted successfully' });
        }

    } catch (error) {
        console.error('Error deleting View:', error);
        return res.status(500).json({ error: 'Failed to delete' });
    }
};


export const getViewById = async (req, res) => {
    const { viewId } = req.params;

    try {
        const view = await View.findOne({
            where: { id : viewId },
            attributes: ['id', 'name', 'structure', 'csvfile_id'], 
            include: [
                {
                    model: ViewPannel,
                    as: 'viewPanels',
                    required: false, // Allows workflows without components
                },
            ]
        });
        if (!view) {
            return res.status(404).json({ error: 'View not found' });
        }

        return res.status(200).json({ message: 'View fetched successfully' , data: view });
    } catch (error) {
        console.error('Error fetching view by id:', error);
        return res.status(500).json({ error: 'Failed to fetch view' });
    }
};

export const updateView = async (req, res) => {
    const { viewId } = req.params;
    const { view: viewData, panel: panelData } = req.body;

    try {
        // Find the view
        const view = await View.findOne({
            where: { id : viewId }
        });

        if (!view) {
            return res.status(404).json({ error: 'View not found' });
        }

        // Update the view structure and file ID
        if (viewData) {
            if (viewData.structure) {
                view.structure = viewData.structure;
            }
            if (viewData.csvfile_id) {
                view.csvfile_id = viewData.csvfile_id;
            }
            await view.save();
        }

        // Update or create panels
        if (panelData && Array.isArray(panelData)) {
            // Delete existing panels for this view
            await ViewPannel.destroy({
                where: { views_id: viewId }
            });

            // Create new panels
            for (const panel of panelData) {
                await ViewPannel.create({
                    views_id: viewId,
                    pannel_type_id: panel.panel_type_id,
                    configuration: panel.configuration
                });
            }
        }

        return res.status(200).json({
            status: 200,
            message: 'View updated successfully',
            data: {
                id: view.id,
                name: view.name,
                structure: view.structure
            }
        });
    } catch (error) {
        console.error('Error updating view:', error);
        return res.status(500).json({ status: 500, error: 'Failed to update view' });
    }
};


export const getPannelTypes = async (req, res) => {
    try {
        const pannelTypes = await PannelType.findAll({
            order: [['created_at', 'DESC']],
        });

        return res.status(200).json({
            message: "pannel Types fetched successfully.",
            data: pannelTypes
             });
    } catch (error) {
        console.error('Error fetching pannel types:', error);
        return res.status(500).json({ error: 'Failed to fetch pannel types' });
    }
};

