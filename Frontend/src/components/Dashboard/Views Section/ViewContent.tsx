import React, { useRef } from 'react';
import { FileData, ComponentType, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import GridLayout from './GridLayout';
import { Button, message } from 'antd';
import { SaveOutlined, ClearOutlined } from '@ant-design/icons';
import { putRequest } from '../../../utils/apiHandler';
import Notiflix from 'notiflix';
import TabsInterface from './Tabs/TabsInterface';
import AppliedFilters from './AppliedFilters';
import './ViewStyles.css';

interface ViewContentProps {
  selectedFile: FileData | null;
  filteredData?: any; // Pre-filtered data
  createInitialPanels?: boolean;
  activePanels?: ComponentType[];
  onPanelsChange?: (activePanels: ComponentType[]) => void;

  // Selection and filter props
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  panelFilters?: Record<string, PanelFilter[]>;
  conditionalFilters?: PanelFilter[];

  // Selection and filter handlers

  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string , data?:any) => void;
  onClearAllFilters?: () => void;

  // Filter management handlers
  onAddFilter?: (filter: PanelFilter, panelId: string) => void;
  onRemoveFilter?: (filterId: string, panelId: string) => void;

  // View structure from database
  structure?: any;
}

const ViewContent: React.FC<ViewContentProps> = ({
  selectedFile,
  filteredData,
  createInitialPanels = false,
  onPanelsChange,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = {},
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onClearAllFilters,
  onAddFilter,
  onRemoveFilter,
  structure
}) => {
  // Reference to the GridLayout component
  const gridLayoutRef = useRef<{getLayout: () => any[], getItems: () => any[]}>(null);

  // Helper function to map component types to panel type IDs
  const getPanelTypeId = (componentType: ComponentType): number => {
    switch (componentType) {
      case ComponentType.TimeSeriesPanel:
        return 1;
      case ComponentType.OverviewPanel:
        return 2;
      case ComponentType.HistogramPanel:
        return 3;
      case ComponentType.DataTablePanel:
        return 4;
      default:
        return 0;
    }
  };

  // Function to handle saving the view
  const handleSaveView = async () => {
    if (!selectedFile || !gridLayoutRef.current) {
      message.error('Please select a file first');
      return;
    }

    try {
      Notiflix.Loading.circle('Saving view...');

      // Get the current layout and items from the GridLayout component
      const currentLayout = gridLayoutRef.current.getLayout();
      const currentItems = gridLayoutRef.current.getItems();

      if (!currentLayout || !currentItems || currentLayout.length === 0) {
        message.error('No layout or items found');
        Notiflix.Loading.remove();
        return;
      }

      // Get the view ID from the URL if it exists
      const searchParams = new URLSearchParams(window.location.search);
      const viewId = searchParams.get('viewId');

      // Create a combined layout with panel types
      const layout = currentLayout.map((layoutItem: any) => {
        // Find the corresponding item to get its type
        const item = currentItems.find((i: any) => i.id === layoutItem.i);
        return {
          ...layoutItem,
          panelType: item ? item.type : ComponentType.TimeSeriesPanel
        };
      });

      // Create panel data for each panel
      const panels = currentItems.map((item: any) => {
        // Find the corresponding layout item
        const layoutItem = currentLayout.find((l: any) => l.i === item.id) || { x: 0, y: 0, w: 6, h: 6 };

        // Get panel-specific filters
        let panelSpecificFilters = {};
        if (panelFilters && panelFilters[item.id]) {
          panelSpecificFilters = { filters: panelFilters[item.id] };
        }

        // For overview panel, save the checkbox state
        if (item.type === 'overview' && selectedColumns && selectedColumns.indices.length > 0) {
          panelSpecificFilters = {
            ...panelSpecificFilters,
            selectedColumns: selectedColumns
          };
        }

        return {
          view_id: viewId || 'new_view',
          panel_type_id: getPanelTypeId(item.type),
          configuration: {
            title: item.title || 'Panel',
            position: {
              x: layoutItem.x,
              y: layoutItem.y,
              w: layoutItem.w,
              h: layoutItem.h
            },
            // Include panel filters that apply to all panels
            panelFilters: {
              selectedColumns: selectedColumns,
              dateFilter: dateFilter,
              conditionalFilters: conditionalFilters
            },
            // Include panel-specific filters
            ...panelSpecificFilters
          }
        };
      });

      if (viewId) {
        // If viewId exists, update the existing view
        const updateData = {
          view: {
            structure: layout,
            csvfile_id: selectedFile.csv_id
          },
          panel: panels
        };

        // Update the view structure
        const response = await putRequest(`/view/${viewId}`, updateData);

        if (response.data && response.data.status === 200) {
          message.success('View updated successfully');
          Notiflix.Loading.remove();
        } else {
          message.error('Failed to update view');
          Notiflix.Loading.remove();
        }
      } else {
        // If no viewId, this is an error - views should be created from the folder structure
        message.error('No view ID found. Please create a view from the folder structure first.');
        Notiflix.Loading.remove();
      }
    } catch (error) {
      console.error('Error saving view:', error);
      message.error('Error saving view');
      Notiflix.Loading.remove();
    }
  };



  return (
    <>
    <TabsInterface />
    <div className="view-content flex-1 p-4 h-full relative">
      {selectedFile ? (
        <div className="h-full">
          {/* Applied Filters Section */}
          <div className="flex flex-wrap items-center mb-4">
            <div className="flex-grow">
              <AppliedFilters
                selectedColumns={selectedColumns}
                dateFilter={dateFilter}
                conditionalFilters={conditionalFilters}
                onRemoveFilter={onRemoveFilter || (() => {})}
                onClearAllFilters={onClearAllFilters || (() => {})}
              />
            </div>

            {/* Buttons */}
            <div className="flex gap-2">
              <Button
                type="default"
                icon={<ClearOutlined />}
                onClick={() => {
                  if (onClearAllFilters) {
                    onClearAllFilters();
                  } else {
                    // Fallback to legacy approach
                    if (onColumnSelection) onColumnSelection([], []);
                    if (onDateFilterChange) onDateFilterChange(null, null);
                  }
                }}
                style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }}
              >
                Clear Filters
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveView}
                style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }}
              >
                Save View
              </Button>
            </div>
          </div>

          <GridLayout
            ref={gridLayoutRef}
            selectedFile={selectedFile}
            filteredData={filteredData}
            createInitialPanels={createInitialPanels}
            onPanelsChange={onPanelsChange}
            selectedColumns={selectedColumns}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
            onZoomSelection={onZoomSelection}
            onAddFilter={onAddFilter}
            onRemoveFilter={onRemoveFilter}
            structure={structure}
          />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500">Select a file from the sidebar to view content</p>
        </div>
      )}
    </div>
    </>
  );
};

export default ViewContent;
