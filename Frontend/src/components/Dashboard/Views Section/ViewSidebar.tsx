import React, { useState } from 'react';
import { Select, Button, Space, Divider, Spin, message, Dropdown, Menu } from 'antd';
import { ArrowLeftOutlined, QuestionCircleOutlined, DeleteOutlined, MoreOutlined, FilterOutlined ,PlusOutlined ,MinusOutlined} from '@ant-design/icons';
import { ComponentType, FileData } from './types';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { removeAnnotation, toggleOperationType, toggleSelectedAnnotation, updateAnnotation, toggleAnnotationFilter } from '../../../Redux/slices/annotationSlice';
import { toggleSelectedOperation, updateOperation, toggleOperationFilter, removeOperation } from '../../../Redux/slices/operationSlice';
import { Collapse, Tooltip, Popconfirm } from 'antd';
import edit from "../../../img/edit.svg"
import eyeopen from "../../../img/eyeopen.svg";
import eyeclose from "../../../img/eyeclose.svg";
import { SaveOutlined, ClearOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { postRequest } from '../../../utils/apiHandler';
import store from '../../../Redux/store';

// import saveIcon from "../../../img/"
interface ViewSidebarProps {
  files?: FileData[];
  selectedFile?: FileData | null;
  onFileSelect?: (file: FileData) => void;
  onBackClick?: () => void;
  activePanels?: ComponentType[];
  isLoading?: boolean;
}

const ViewSidebar: React.FC<ViewSidebarProps> = ({
  files = [],
  selectedFile = null,
  onFileSelect,
  onBackClick,
  activePanels = [],
  isLoading = false
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const annotations = useSelector((state: any) => state.annotations);
  console.log('annotations annotations annotations annotations annotations annotations:', annotations);
  // const selectedAnnotations = useSelector((state: any) => state.annotations.selectedAnnotations.find((res:any)=> res.columnName == annotations?.activeColumnName)?.annotations);
  // // console.log('selectedAnnotations ================================================================================', selectedAnnotations);

  // Operations state
  const operations = useSelector((state: any) => state.operations);
  const selectedOperations = useSelector((state: any) => state.operations.selectedOperations.find((res: any) => res.columnName == operations?.activeColumnName)?.operations);

  const [editingOperationIndex, setEditingOperationIndex] = useState<number | null>(null);
  const [editedOperationValue, setEditedOperationValue] = useState<any>('');

  const { Panel } = Collapse;
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedValue, setEditedValue] = useState<any>('');

  const selectSystems = useSelector((state: any) => state.systems.systems);


  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      // Default behavior: navigate back to workflow folders
      navigate('/?tab=insight&workflowId=0');
    }
  };

  const handleFileChange = (fileId: string) => {
    const file = files.find(f => f.csv_id === fileId);
    if (file && onFileSelect) {
      onFileSelect(file);
    }
  };

  const togglePanel = (annotation: any) => {
    let data = {
      columnName: annotations?.activeColumnName,
      shapeId: annotation.shapeId
    }
    dispatch(toggleSelectedAnnotation(data));
  };

  const toggleOperationPanel = (operation: any) => {
    let data = {
      columnName: operations?.activeColumnName,
      operation: operation
    }
    dispatch(toggleSelectedOperation(data));
  };

  const handleSaveEdit = (annotation: any, value: any) => {
    let data = {
      columnName: annotations?.activeColumnName,
      shapeId: annotation?.shapeId,
      newLabelText: value
    }
    dispatch(updateAnnotation(data))
  }

  const handleSaveOperationEdit = (operation: any, value: any) => {
    let data = {
      columnName: operations?.activeColumnName,
      operationId: operation?.operationId,
      newLabelText: value
    }
    dispatch(updateOperation(data))
  }

  const handleOperationType = (annotation: any) => {
    console.log('annotation :', annotation);
    let data = {
      columnName: annotations?.activeColumnName,
      shapeId: annotation?.shapeId,
    }
    dispatch(toggleOperationType(data))
  }

  const handleToggleAnnotationFilter = (annotation: any) => {
    console.log('Toggling annotation filter:', annotation);
    let data = {
      columnName: annotations?.activeColumnName,
      shapeId: annotation?.shapeId,
    }

    // Toggle the annotation filter
    dispatch(toggleAnnotationFilter(data));

    // If we're applying the filter (annotation wasn't previously a filter),
    // also unselect it so it doesn't show on the graph
    if (!annotation.applyAsFilter) {
      // If the annotation is currently selected, unselect it
      if (annotation.selected) {
        dispatch(toggleSelectedAnnotation(data));
      }
    }
  }

  const handleToggleOperationFilter = (operation: any) => {
    console.log('Toggling operation filter:', operation);

    // If we're applying a new filter (operation wasn't previously a filter)
    if (!operation.applyAsFilter) {
      // First, remove any existing operation filters and uncheck their checkboxes
      if (operations?.operations) {
        operations.operations.forEach((group: any) => {
          if (group.columnName === operations?.activeColumnName) {
            group.operations.forEach((op: any) => {
              if (op.applyAsFilter) {
                // Remove this filter
                dispatch(toggleOperationFilter({
                  columnName: operations?.activeColumnName,
                  operationId: op.operationId
                }));

                // Also uncheck the checkbox if it's checked
                const selectedGroup = selectedOperations?.find((group: any) =>
                  group.columnName === operations?.activeColumnName
                );

                if (selectedGroup && selectedGroup.operations.some((selOp: any) =>
                  selOp.operationId === op.operationId
                )) {
                  dispatch(toggleSelectedOperation({
                    columnName: operations?.activeColumnName,
                    operation: op
                  }));
                }
              }
            });
          }
        });
      }

      // Now apply the new filter
      let data = {
        columnName: operations?.activeColumnName,
        operationId: operation?.operationId,
      }
      dispatch(toggleOperationFilter(data));

      // Always uncheck the checkbox for the operation being filtered
      const selectedGroup = selectedOperations?.find((group: any) =>
        group.columnName === operations?.activeColumnName
      );

      if (selectedGroup && selectedGroup.operations.some((selOp: any) =>
        selOp.operationId === operation?.operationId
      )) {
        dispatch(toggleSelectedOperation({
          columnName: operations?.activeColumnName,
          operation: operation
        }));
      }
    } else {
      // If we're removing a filter, toggle it off
      let data = {
        columnName: operations?.activeColumnName,
        operationId: operation?.operationId,
      }
      dispatch(toggleOperationFilter(data));

      // When removing a filter, we want to make the operation visible in the chart
      // So we need to check the checkbox if it's not already checked
      const selectedGroup = selectedOperations?.find((group: any) =>
        group.columnName === operations?.activeColumnName
      );

      const isAlreadySelected = selectedGroup && selectedGroup.operations.some((selOp: any) =>
        selOp.operationId === operation?.operationId
      );

      // If not already selected, select it to make it visible in the chart
      if (!isAlreadySelected) {
        dispatch(toggleSelectedOperation({
          columnName: operations?.activeColumnName,
          operation: operation
        }));
      }
    }
  }

  const handleSaveAnnotations = async (idDelete?: any) => {
    try {
      const state = store.getState(); // Import your store if needed
      const currentAnnotations = state.annotations;
      const currentOperations = state.operations
      let anomalyToSave = currentAnnotations?.annotations?.find((res: any) => res?.columnName === annotations?.activeColumnName)?.annotations || [];
      let operationsToSave = currentOperations?.operations?.find((res: any) => res?.columnName === annotations?.activeColumnName)?.operations || []
      const annotationToSave = [...anomalyToSave, ...operationsToSave];

      if (annotationToSave && annotationToSave?.length > 0) {
        let payload = {
          annotations: annotationToSave,
          columnName: annotations?.activeColumnName,
          systems: JSON.stringify(selectSystems[0]?.systems || [])
        }
        let response = await postRequest('/file/upload-json-file', payload);

        if (response?.data) {
          message.success(idDelete ? 'Annotations Updated successfully' : 'Annotations saved successfully');
        }
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  const deleteAnnotation = async (annotation: any) => {
    await removeAnomalyFromRedux(annotation)
    await handleSaveAnnotations(true)
  }

  const deleteOperationalRanges = async (operationalRanges: any) => {
    await removeOperatonalRangesFromRedux(operationalRanges)
    await handleSaveAnnotations(true)
  }

  const removeOperatonalRangesFromRedux = async (operationalRanges: any) => {
    let data = {
      columnName: annotations?.activeColumnName,
      operationId: operationalRanges.operationId
    }
    dispatch(removeOperation(data))
  }

  const removeAnomalyFromRedux = async (annotation: any) => {
    let data = {
      columnName: annotations?.activeColumnName,
      shapeId: annotation.shapeId
    }
    dispatch(removeAnnotation(data))
  }
  return (
    <div className="view-sidebar pl-0 pt-4 pb-4 pr-4  h-full flex flex-col shadow-none">
      <div className="sidebar-header mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackClick}
            style={{ padding: 0 }}
          >
            Back to Views
          </Button>

          {isLoading ? (
            <div style={{ textAlign: 'center', padding: '10px 0' }}>
              <Spin size="small" /> Loading files...
            </div>
          ) : (
            <Select
              placeholder="Select a file"
              style={{ width: '100%' }}
              value={selectedFile?.csv_id || undefined}
              onChange={handleFileChange}
              options={files.map(file => ({
                value: file.csv_id,
                label: file.file_name
              }))}
            />
          )}
        </Space>
      </div>

      {/* <Divider style={{ margin: '12px 0' }} /> */}
      <Collapse defaultActiveKey={['1','2']} 
    expandIconPosition="end"
    expandIcon={({ isActive }) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {
          isActive ?<MinusOutlined style={{ fontSize: '16px' }} />:<div><PlusOutlined style={{ fontSize: '16px' }} /></div>
        }
      </div>
    )}
    className="outer-collapse"
     >
        <Panel header={
                       <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                       <h2 className="text-base font-medium mb-3" style={{ margin: 0 }}> Available Panels</h2>
                      </div>
                     } 
                     key="1"
                     style={{
                      borderRadius: 0, 
                      backgroundColor: '#E9E9F5',
                      marginBottom:'15px',
                      border: '1px solid #b7b6b6'
                    }}>
      {selectedFile && (
        <div className="sidebar-components flex-grow">
          {/* <h3 className="text-base font-medium mb-3">Available Panels</h3> */}
          <ul className="component-list">
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.TimeSeriesPanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.TimeSeriesPanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.TimeSeriesPanel)) return;
                e.dataTransfer.setData('component', ComponentType.TimeSeriesPanel);
                console.log('Drag started: TimeSeriesPanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Time Series {activePanels.includes(ComponentType.TimeSeriesPanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.OverviewPanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.OverviewPanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.OverviewPanel)) return;
                e.dataTransfer.setData('component', ComponentType.OverviewPanel);
                console.log('Drag started: OverviewPanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Overview {activePanels.includes(ComponentType.OverviewPanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.HistogramPanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.HistogramPanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.HistogramPanel)) return;
                e.dataTransfer.setData('component', ComponentType.HistogramPanel);
                console.log('Drag started: HistogramPanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Histogram {activePanels.includes(ComponentType.HistogramPanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.DataTablePanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.DataTablePanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.DataTablePanel)) return;
                e.dataTransfer.setData('component', ComponentType.DataTablePanel);
                console.log('Drag started: DataTablePanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Data Table {activePanels.includes(ComponentType.DataTablePanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
          </ul>
        </div>
      )}

      {!selectedFile && (
        <div className="flex-grow flex items-center justify-center text-gray-500">
          <p>Select a file to view available panels</p>
        </div>
      )}
 </Panel>
 <Panel header={
                       <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                       <h2 className="text-base font-medium mb-3" style={{ margin: 0 }}>Annotations</h2>
                      </div>
                     }  key="2"
                     style={{
                      borderRadius: 0, 
                      backgroundColor: '#E9E9F5',
                      border: '1px solid #b7b6b6'
                    }}>
      {selectedFile && (
        <div className='m-0'>
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
                type="primary"
                icon={<SaveOutlined />}
                size="small"
                onClick={handleSaveAnnotations}
                style={{
                  fontSize: '12px',
                  boxShadow: '0 1px 4px rgba(0, 0, 0, 0.15)',
                  height: '24px',
                  padding: '0 8px',
                  right:'0px'
                }}
              >
                Save Annotations
              </Button>
          </div>
          <div className="mt-4 border" style={{ borderColor: '#b7b6b6' }}>
            <div className="flex items-center space-x-2 bg-[#E9E9F5] p-2 rounded">
              <h2 className="text-sm font-semibold m-0">Anomaly</h2>
              <span className="h-3 w-3 rounded-full bg-[#ff3030] inline-block"></span>
            </div>
            <div className="space-y-2">
              <Collapse accordion expandIcon={() => null}
                   style={{
                    borderRadius: 0, // Remove border radius from the Collapse container
                  }}>
                {annotations?.annotations
                  ?.find((res: any) => res?.columnName === annotations?.activeColumnName)
                  ?.annotations?.map((annotation: any, index: any) => (
                    <Panel
                      key={index}
                      header={
                        <div className="flex justify-start items-center w-full">
                          <div
                            onClick={(e) => {
                              e.stopPropagation();
                              togglePanel(annotation);
                            }}
                            className="cursor-pointer h-5 w-5 flex items-center justify-center"
                          >
                            <img
                              src={annotation?.selected ? eyeopen : eyeclose}
                              alt={annotation?.selected ? "Visible" : "Hidden"}
                              className="h-5 w-5"
                            />
                          </div>
                          <div className="w-full">
                            {editingIndex === index ? (
                              <div className="flex items-center gap-1 pl-1 w-full" >
                                <input
                                  type="text"
                                  value={editedValue}
                                  onChange={(e) => {
                                    setEditedValue(e.target.value)
                                  }}
                                  onClick={(e) => e.stopPropagation()}
                                  className="border-2 rounded text-sm w-full"
                                  style={{ borderColor: '#252963' }}
                                />

                                <CheckOutlined
                                  title="Save"
                                  className="text-green-600 cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSaveEdit(annotation, editedValue);
                                    setEditingIndex(null);
                                  }}
                                />
                                <CloseOutlined
                                  className="text-red-600 cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditingIndex(null)
                                  }
                                  }
                                />
                              </div>
                            ) : (
                              <div
                                className="flex items-center w-full cursor-pointer"
                              >
                                <span className="text-sm text-gray-800 pl-2 block truncate max-w-[150px]" title={annotation.label.text}>
                                  {annotation.label.text}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      }
                      extra={
                        <div className="flex items-center gap-2">
                          {editingIndex === index ? (
                            <div>
                            </div>
                          ) : (
                            <div className='flex gap-2' onClick={(e) => e.stopPropagation()}>
                              <Tooltip title="Edit">
                                <img
                                  src={edit}
                                  className="cursor-pointer"
                                  onClick={() => {
                                    setEditingIndex(index);
                                    setEditedValue(`${annotation.label.text}`);
                                  }}
                                />
                              </Tooltip>

                              <Button
                                type={annotation?.applyAsFilter ? "primary" : "default"}
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleAnnotationFilter(annotation);
                                }}
                                className={annotation?.applyAsFilter ? "bg-[#252963]" : ""}
                                style={{ fontSize: '12px', padding: '0 8px', height: '24px' }}
                              >
                                {annotation?.applyAsFilter ? "Remove" : "Apply"}
                              </Button>

                              <Dropdown
                                menu={{
                                  items: [
                                    {
                                      key: '1',
                                      label: (
                                        <Popconfirm
                                          title="Delete Annotation"
                                          description="Are you sure to delete this Annotation?"
                                          icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                                          onConfirm={() => deleteAnnotation(annotation)}
                                        >
                                          <div className="flex items-center gap-2 text-red-600">
                                            <DeleteOutlined />
                                            <span>Delete Anomaly</span>
                                          </div>
                                        </Popconfirm>
                                      ),
                                    }
                                  ]
                                }}
                                trigger={['click']}
                              >
                                <MoreOutlined
                                  className="cursor-pointer text-gray-600"
                                  style={{ fontSize: '18px' }}
                                />
                              </Dropdown>
                            </div>
                          )}
                        </div>
                      }
                    >
                      <div className="p-1 pl-2 border rounded-lg shadow-sm bg-white">
                        <p className="text-sm text-gray-800 ">
                          Date Time : {annotation?.x0 ? new Date(annotation.x0).toISOString().slice(0, 16).replace('T', ' ') : 'N/A'}
                        </p>
                        <p className="text-sm text-gray-800">
                          Date Time : {annotation?.x1 ? new Date(annotation.x1).toISOString().slice(0, 16).replace('T', ' ') : 'N/A'}
                        </p>
                        <p className="text-sm text-gray-800">
                          {annotations?.activeColumnName} : {annotation?.y0 || 'N/A'}
                        </p>
                        <p className="text-sm text-gray-800">
                          {annotations?.activeColumnName} : {annotation?.y1 || 'N/A'}
                        </p>
                        <div className="mt-2 flex justify-end items-center">
                          {annotation?.applyAsFilter && (
                            <span className="text-xs text-green-600">
                              Applied as filter
                            </span>
                          )}
                        </div>
                      </div>
                    </Panel>
                  ))}
              </Collapse>
            </div>
          </div>

          <div className="mt-4 border" style={{ borderColor: '#b7b6b6' }}>
            <div className="flex items-center space-x-2 bg-[#E9E9F5] p-2 rounded">
              <h2 className="text-sm font-semibold m-0">Operation Ranges</h2>
              <span className="h-3 w-3 rounded-full bg-[#0c631f] inline-block"></span>
            </div>
            <div className="space-y-2">
              <Collapse accordion expandIcon={() => null}
              style={{
                borderRadius: 0, // Remove border radius from the Collapse container
              }}>
                {operations?.operations
                  ?.find((res: any) => res?.columnName === operations?.activeColumnName)
                  ?.operations?.map((operation: any, index: any) => (
                    <Panel
                      key={index}
                      header={
                        <div className="flex justify-start items-center w-full">
                          <div
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleOperationPanel(operation);
                            }}
                            className="cursor-pointer h-5 w-5 flex items-center justify-center"
                          >
                            <img
                              src={operation?.selected ? eyeopen : eyeclose}
                              alt={operation?.selected ? "Visible" : "Hidden"}
                              className="h-5 w-5"
                            />
                          </div>
                          <div className="w-full" >
                            {editingOperationIndex === index ? (
                              <div className="flex items-center gap-1 pl-1 w-full" onClick={(e) => e.stopPropagation()}>
                                <input
                                  type="text"
                                  value={editedOperationValue}
                                  onChange={(e) => {
                                    setEditedOperationValue(e.target.value)
                                  }}
                                  className="border-2 rounded text-sm w-full"
                                  style={{ borderColor: '#252963' }}
                                />

                                <CheckOutlined
                                  title="Save"
                                  className="text-green-600 cursor-pointer"
                                  onClick={() => {
                                    handleSaveOperationEdit(operation, editedOperationValue);
                                    setEditingOperationIndex(null);
                                  }}
                                />
                                <CloseOutlined
                                  className="text-red-600 cursor-pointer"
                                  onClick={() => setEditingOperationIndex(null)}
                                />
                              </div>
                            ) : (
                              <div className="flex items-center w-full cursor-pointer">
                                <span
                                  className="text-sm text-gray-800 pl-2 block truncate max-w-[150px]"
                                  title={operation.label?.text || `Operation Range ${operation.y0.toFixed(2)} - ${operation.y1.toFixed(2)}`}
                                >
                                  {operation.label?.text || `Operation Range ${operation.y0.toFixed(2)} - ${operation.y1.toFixed(2)}`}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      }
                      extra={
                        <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
                          {editingOperationIndex === index ? (
                            <div></div>
                          ) : (
                            <div className='flex gap-2'>
                              <Tooltip title="Edit">
                                <img
                                  src={edit}
                                  className="cursor-pointer"
                                  onClick={() => {
                                    setEditingOperationIndex(index);
                                    setEditedOperationValue(operation.label?.text || `Operation Range ${operation.y0.toFixed(2)} - ${operation.y1.toFixed(2)}`);
                                  }}
                                />
                              </Tooltip>

                              <Button
                                type={operation?.applyAsFilter ? "primary" : "default"}
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleOperationFilter(operation);
                                }}
                                className={operation?.applyAsFilter ? "bg-[#4CAF50]" : ""}
                                style={{
                                  fontSize: '12px',
                                  padding: '0 8px',
                                  height: '24px',
                                  backgroundColor: operation?.applyAsFilter ? "#4CAF50" : ""
                                }}
                              >
                                {operation?.applyAsFilter ? "Remove" : "Apply"}
                              </Button>

                              <Dropdown
                                menu={{
                                  items: [
                                    {
                                      key: '1',
                                      label: (
                                        <Popconfirm
                                          title="Delete Operation Range"
                                          description="Are you sure to delete this Operation Range?"
                                          icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                                          onConfirm={() => deleteOperationalRanges(operation)}
                                        >
                                          <div className="flex items-center gap-2 text-red-600">
                                            <DeleteOutlined />
                                            <span>Delete Operational Range</span>
                                          </div>
                                        </Popconfirm>
                                      ),
                                    }
                                  ]
                                }}
                                trigger={['click']}
                              >
                                <MoreOutlined
                                  className="cursor-pointer text-gray-600"
                                  style={{ fontSize: '18px' }}
                                />
                              </Dropdown>
                            </div>
                          )}
                        </div>
                      }
                    >
                      <div className="p-1 pl-2 border rounded-lg shadow-sm bg-white">
                        <p className="text-sm text-gray-800">
                          {operations?.activeColumnName} Lower Bound: {operation?.y0.toFixed(2) || 'N/A'}
                        </p>
                        <p className="text-sm text-gray-800">
                          {operations?.activeColumnName} Upper Bound: {operation?.y1.toFixed(2) || 'N/A'}
                        </p>
                        <div className="mt-2 flex justify-end items-center">
                          {operation?.applyAsFilter && (
                            <span className="text-xs text-green-600">
                              Applied as filter
                            </span>
                          )}
                        </div>
                      </div>
                    </Panel>
                  ))}
              </Collapse>
            </div>
          </div>
        </div>
      )}
  </Panel>
</Collapse>
    </div>
  );
};

export default ViewSidebar;
