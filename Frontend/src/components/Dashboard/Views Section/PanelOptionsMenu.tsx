import React, { useState } from 'react';
import { Dropdown, Button, Modal, Input, Form, message } from 'antd';
import { EllipsisOutlined, DownloadOutlined, SaveOutlined } from '@ant-design/icons';
import { postRequest } from '../../../utils/apiHandler';
import Notiflix from 'notiflix';

// Dynamic imports to handle cases where dependencies might not be installed
const importHtml2Canvas = () => import('html2canvas').catch(() => {
  message.error('html2canvas library is not installed. Please run: npm install html2canvas');
  return null;
});

const importJsPDF = () => import('jspdf').catch(() => {
  message.error('jspdf library is not installed. Please run: npm install jspdf');
  return null;
});


interface PanelOptionsMenuProps {
  panelType: string;
  panelRef: React.RefObject<HTMLDivElement>;
  panelTitle: string;
  configuration: any;
  fileId?: string | number;
}

const PanelOptionsMenu: React.FC<PanelOptionsMenuProps> = ({
  panelType,
  panelRef,
  panelTitle,
  configuration,
  fileId
}) => {
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [form] = Form.useForm();

  // Export panel as image
  const exportAsImage = async () => {
    if (!panelRef.current) return;

    try {
      message.loading('Generating image...', 0);

      // Dynamically import html2canvas
      const html2canvasModule = await importHtml2Canvas();
      if (!html2canvasModule) {
        message.destroy();
        return;
      }

      const html2canvas = html2canvasModule.default;

      // Create a clone of the panel to manipulate without affecting the UI
      const panelClone = panelRef.current.cloneNode(true) as HTMLElement;
      document.body.appendChild(panelClone);

      // Set styles to ensure all content is visible
      panelClone.style.position = 'absolute';
      panelClone.style.left = '-9999px';
      panelClone.style.top = '0';
      panelClone.style.width = `${panelRef.current.offsetWidth}px`;
      panelClone.style.height = 'auto';
      panelClone.style.maxHeight = 'none';
      panelClone.style.overflow = 'visible';
      panelClone.style.transform = 'none';
      panelClone.style.maxWidth = 'none';

      // Make sure all child elements are visible
      const scrollableElements = panelClone.querySelectorAll('[style*="overflow"]');
      scrollableElements.forEach((el: any) => {
        if (el.style.overflow === 'auto' || el.style.overflow === 'scroll' ||
            el.style.overflowY === 'auto' || el.style.overflowY === 'scroll') {
          el.style.overflow = 'visible';
          el.style.overflowY = 'visible';
          el.style.height = 'auto';
          el.style.maxHeight = 'none';
        }
      });

      // Render the full panel
      const canvas = await html2canvas(panelClone, {
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: panelRef.current.offsetWidth,
        height: panelClone.scrollHeight,
        windowWidth: panelRef.current.offsetWidth,
        windowHeight: panelClone.scrollHeight
      });

      // Remove the clone from the DOM
      document.body.removeChild(panelClone);

      message.destroy();

      // Create a link element to download the image
      const link = document.createElement('a');
      link.download = `${panelTitle.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().slice(0, 10)}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();

      message.success('Image exported successfully');
    } catch (error) {
      message.destroy();
      message.error('Failed to export image');
      console.error('Error exporting image:', error);
    }
  };

  // Export panel as PDF
  const exportAsPDF = async () => {
    if (!panelRef.current) return;

    try {
      message.loading('Generating PDF...', 0);

      // Dynamically import dependencies
      const [html2canvasModule, jsPDFModule] = await Promise.all([
        importHtml2Canvas(),
        importJsPDF()
      ]);

      if (!html2canvasModule || !jsPDFModule) {
        message.destroy();
        return;
      }

      const html2canvas = html2canvasModule.default;
      const jsPDF = jsPDFModule.default;

      // Create a clone of the panel to manipulate without affecting the UI
      const panelClone = panelRef.current.cloneNode(true) as HTMLElement;
      document.body.appendChild(panelClone);

      // Set styles to ensure all content is visible
      panelClone.style.position = 'absolute';
      panelClone.style.left = '-9999px';
      panelClone.style.top = '0';
      panelClone.style.width = `${panelRef.current.scrollWidth}px`;
      panelClone.style.height = 'auto';
      panelClone.style.maxHeight = 'none';
      panelClone.style.overflow = 'visible';
      panelClone.style.transform = 'none';
      panelClone.style.maxWidth = 'none';
      panelClone.style.backgroundColor = '#ffffff';
  
      // Fix scrollable children to show all content
      const scrollables = panelClone.querySelectorAll('*');
      scrollables.forEach((el: any) => {
        const style = window.getComputedStyle(el);
        if (
          ['auto', 'scroll'].includes(style.overflow) ||
          ['auto', 'scroll'].includes(style.overflowY)
        ) {
          el.style.overflow = 'visible';
          el.style.overflowY = 'visible';
          el.style.height = 'auto';
          el.style.maxHeight = 'none';
        }
      });
  
      // Wait for any async renderings (like fonts, graphs) to finish
      await new Promise((resolve) => setTimeout(resolve, 500));
  
      // Render canvas from cloned panel
      const canvas = await html2canvas(panelClone, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: panelClone.scrollWidth,
        height: panelClone.scrollHeight,
        windowWidth: panelClone.scrollWidth,
        windowHeight: panelClone.scrollHeight,
      });
  
      document.body.removeChild(panelClone); // Clean up
      message.destroy();
  
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4',
      });
  
      // Fit image to PDF
      const pageWidth = pdf.internal.pageSize.getWidth() - 20; // 10mm margin each side
      const imgHeight = (canvas.height * pageWidth) / canvas.width;
  
      // Check if image fits or needs multiple pages
      let position = 10;
      if (imgHeight <= pdf.internal.pageSize.getHeight() - 20) {
        // Single page
        pdf.addImage(imgData, 'PNG', 10, position, pageWidth, imgHeight);
      } else {
        // Multi-page handling
        const pageHeight = pdf.internal.pageSize.getHeight() - 20;
        let remainingHeight = imgHeight;
        let canvasPosition = 0;
  
        while (remainingHeight > 0) {
          const chunkHeight = Math.min(remainingHeight, pageHeight);
  
          const pageCanvas = document.createElement('canvas');
          pageCanvas.width = canvas.width;
          pageCanvas.height = (chunkHeight * canvas.width) / pageWidth;
          const ctx = pageCanvas.getContext('2d')!;
          ctx.drawImage(
            canvas,
            0,
            canvasPosition,
            canvas.width,
            pageCanvas.height,
            0,
            0,
            canvas.width,
            pageCanvas.height
          );
  
          const pageImgData = pageCanvas.toDataURL('image/png');
          if (canvasPosition === 0) {
            pdf.addImage(pageImgData, 'PNG', 10, 10, pageWidth, chunkHeight);
          } else {
            pdf.addPage();
            pdf.addImage(pageImgData, 'PNG', 10, 10, pageWidth, chunkHeight);
          }
  
          remainingHeight -= chunkHeight;
          canvasPosition += pageCanvas.height;
        }
      }
  
      const filename = `${panelTitle.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().slice(0, 10)}.pdf`;
      pdf.save(filename);
  
      message.success('PDF exported successfully');
    } catch (error) {
      message.destroy();
      message.error('Failed to export PDF');
      console.error('Error exporting PDF:', error);
    }
  };

  // Save panel as a separate view
  const saveAsView = async (values: { viewName: string }) => {
    try {
      Notiflix.Loading.standard('Saving view...');


      // Get the current file ID from the global variable or from props
      const csvFileId = fileId

      if (!csvFileId) {
        message.error('No file selected. Please select a file first.');
        Notiflix.Loading.remove();
        return;
      }

      // Create a new view with just this panel
      const viewData = {
        csvfile_id: csvFileId,
        name: values.viewName,
        description: `Created from ${panelTitle} panel`,
        panels: [
          {
            panel_type_id: getPanelTypeId(panelType),
            configuration: {
              title: panelTitle,
              position: {
                x: 0,
                y: 0,
                w: 12,
                h: 8
              },
              ...configuration
            }
          }
        ],
        structure: [
          {
            i: 'item-1',
            x: 0,
            y: 0,
            w: 12,
            h: 8,
            panelType: panelType
          }
        ]
      };

      // Create the view using POST request
      const response = await postRequest('/view/create-view', viewData);

      Notiflix.Loading.remove();

      if (response.status === 200 || response.status === 201) {
        message.success(`Panel saved as view: ${values.viewName}`);
        setSaveModalVisible(false);
        form.resetFields();
      } else {
        message.error('Failed to save view');
      }
    } catch (error) {
      Notiflix.Loading.remove();
      message.error('Failed to save view');
      console.error('Error saving view:', error);
    }
  };

  // Get panel type ID based on panel type string
  const getPanelTypeId = (type: string) => {
    switch (type) {
      case 'TimeSeriesPanel':
        return 1;
      case 'OverviewPanel':
        return 2;
      case 'HistogramPanel':
        return 3;
      case 'DataTablePanel':
        return 4;
      default:
        return 1;
    }
  };

  // Define menu items for the dropdown
  const menuItems = [
    {
      key: 'export',
      label: 'Export',
      icon: <DownloadOutlined />,
      children: [
        {
          key: 'image',
          label: 'Export as Image',
          onClick: exportAsImage
        },
        {
          key: 'pdf',
          label: 'Export as PDF',
          onClick: exportAsPDF
        }
      ]
    },
    {
      key: 'save',
      label: 'Save as View',
      icon: <SaveOutlined />,
      onClick: () => setSaveModalVisible(true)
    }
  ];

  return (
    <>
      <Dropdown menu={{ items: menuItems }} trigger={['click']} placement="bottomRight">
        <Button
          type="text"
          icon={<EllipsisOutlined />}
          className="panel-options-button"
          onClick={(e) => e.stopPropagation()}
        />
      </Dropdown>

      <Modal
        title="Save Panel as View"
        open={saveModalVisible}
        onCancel={() => setSaveModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveAsView}
        >
          <Form.Item
            name="viewName"
            label="View Name"
            rules={[{ required: true, message: 'Please enter a name for the view' }]}
          >
            <Input placeholder="Enter view name" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              Save
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default PanelOptionsMenu;
